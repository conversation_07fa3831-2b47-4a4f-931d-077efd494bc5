import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/calculators/financial_ratios_data.dart';
import '../../services/financial_ratios_service.dart';
import '../../theme/design_tokens.dart';
import '../../theme/responsive_breakpoints.dart';
import '../custom_text_field.dart';

class FinancialRatiosCalculator extends ConsumerStatefulWidget {
  const FinancialRatiosCalculator({super.key});

  @override
  ConsumerState<FinancialRatiosCalculator> createState() => _FinancialRatiosCalculatorState();
}

class _FinancialRatiosCalculatorState extends ConsumerState<FinancialRatiosCalculator>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();
  
  // Balance Sheet Controllers
  final _currentAssetsController = TextEditingController();
  final _currentLiabilitiesController = TextEditingController();
  final _totalAssetsController = TextEditingController();
  final _totalEquityController = TextEditingController();
  final _inventoryController = TextEditingController();
  final _cashController = TextEditingController();
  final _accountsReceivableController = TextEditingController();
  final _totalDebtController = TextEditingController();
  final _longTermDebtController = TextEditingController();
  
  // Income Statement Controllers
  final _revenueController = TextEditingController();
  final _netIncomeController = TextEditingController();
  final _costOfGoodsSoldController = TextEditingController();
  final _operatingIncomeController = TextEditingController();
  final _interestExpenseController = TextEditingController();
  final _grossProfitController = TextEditingController();
  
  FinancialRatiosResult? _result;
  bool _isCalculating = false;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    _disposeControllers();
    super.dispose();
  }
  
  void _disposeControllers() {
    _currentAssetsController.dispose();
    _currentLiabilitiesController.dispose();
    _totalAssetsController.dispose();
    _totalEquityController.dispose();
    _inventoryController.dispose();
    _cashController.dispose();
    _accountsReceivableController.dispose();
    _totalDebtController.dispose();
    _longTermDebtController.dispose();
    _revenueController.dispose();
    _netIncomeController.dispose();
    _costOfGoodsSoldController.dispose();
    _operatingIncomeController.dispose();
    _interestExpenseController.dispose();
    _grossProfitController.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final isMobile = context.isMobile;

    return Scaffold(
      body: isMobile
        ? Column(
            children: [
              _buildTabBar(),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildBalanceSheetTab(),
                    _buildIncomeStatementTab(),
                    _buildResultsTab(),
                  ],
                ),
              ),
            ],
          )
        : _buildResponsiveLayout(),
      floatingActionButton: _buildCalculateButton(),
    );
  }

  Widget _buildResponsiveLayout() {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: Column(
            children: [
              Expanded(child: _buildBalanceSheetTab()),
              Expanded(child: _buildIncomeStatementTab()),
            ],
          ),
        ),
        Expanded(
          flex: 1,
          child: _buildResultsTab(),
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: DesignTokens.colorSurface,
      child: TabBar(
        controller: _tabController,
        labelColor: DesignTokens.colorPrimary,
        unselectedLabelColor: DesignTokens.colorOnSurfaceVariant,
        indicatorColor: DesignTokens.colorPrimary,
        tabs: const [
          Tab(
            icon: Icon(Icons.account_balance),
            text: 'Bilan',
          ),
          Tab(
            icon: Icon(Icons.trending_up),
            text: 'Compte de Résultat',
          ),
          Tab(
            icon: Icon(Icons.analytics),
            text: 'Résultats',
          ),
        ],
      ),
    );
  }
  
  Widget _buildBalanceSheetTab() {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: context.responsivePadding(
          mobile: const EdgeInsets.all(12),
          tablet: const EdgeInsets.all(16),
          desktop: const EdgeInsets.all(20),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Données du Bilan',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: DesignTokens.colorPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: ResponsiveBreakpointsExtension(context).responsiveSpacing(mobile: 12, tablet: 16, desktop: 20)),
            
            _buildSectionCard(
              'Actifs',
              [
                CustomTextField(
                  controller: _currentAssetsController,
                  label: 'Actifs Circulants (DH)',
                  keyboardType: TextInputType.number,
                  validator: _validateNumber,
                ),
                const SizedBox(height: 12),
                CustomTextField(
                  controller: _totalAssetsController,
                  label: 'Total Actifs (DH)',
                  keyboardType: TextInputType.number,
                  validator: _validateNumber,
                ),
                const SizedBox(height: 12),
                CustomTextField(
                  controller: _inventoryController,
                  label: 'Stocks (DH)',
                  keyboardType: TextInputType.number,
                  validator: _validateNumber,
                ),
                const SizedBox(height: 12),
                CustomTextField(
                  controller: _cashController,
                  label: 'Trésorerie (DH)',
                  keyboardType: TextInputType.number,
                  validator: _validateNumber,
                ),
                const SizedBox(height: 12),
                CustomTextField(
                  controller: _accountsReceivableController,
                  label: 'Créances Clients (DH)',
                  keyboardType: TextInputType.number,
                  validator: _validateNumber,
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            _buildSectionCard(
              'Passifs',
              [
                CustomTextField(
                  controller: _currentLiabilitiesController,
                  label: 'Dettes à Court Terme (DH)',
                  keyboardType: TextInputType.number,
                  validator: _validateNumber,
                ),
                const SizedBox(height: 12),
                CustomTextField(
                  controller: _totalEquityController,
                  label: 'Capitaux Propres (DH)',
                  keyboardType: TextInputType.number,
                  validator: _validateNumber,
                ),
                const SizedBox(height: 12),
                CustomTextField(
                  controller: _totalDebtController,
                  label: 'Total Dettes (DH)',
                  keyboardType: TextInputType.number,
                  validator: _validateNumber,
                ),
                const SizedBox(height: 12),
                CustomTextField(
                  controller: _longTermDebtController,
                  label: 'Dettes à Long Terme (DH)',
                  keyboardType: TextInputType.number,
                  validator: _validateNumber,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildIncomeStatementTab() {
    return SingleChildScrollView(
      padding: context.responsivePadding(
        mobile: const EdgeInsets.all(12),
        tablet: const EdgeInsets.all(16),
        desktop: const EdgeInsets.all(20),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Données du Compte de Résultat',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: DesignTokens.colorPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: ResponsiveBreakpointsExtension(context).responsiveSpacing(mobile: 12, tablet: 16, desktop: 20)),
          
          _buildSectionCard(
            'Revenus et Charges',
            [
              CustomTextField(
                controller: _revenueController,
                label: 'Chiffre d\'Affaires (DH)',
                keyboardType: TextInputType.number,
                validator: _validateNumber,
              ),
              const SizedBox(height: 12),
              CustomTextField(
                controller: _grossProfitController,
                label: 'Marge Brute (DH)',
                keyboardType: TextInputType.number,
                validator: _validateNumber,
              ),
              const SizedBox(height: 12),
              CustomTextField(
                controller: _costOfGoodsSoldController,
                label: 'Coût des Marchandises Vendues (DH)',
                keyboardType: TextInputType.number,
                validator: _validateNumber,
              ),
              const SizedBox(height: 12),
              CustomTextField(
                controller: _operatingIncomeController,
                label: 'Résultat d\'Exploitation (DH)',
                keyboardType: TextInputType.number,
                validator: _validateNumber,
              ),
              const SizedBox(height: 12),
              CustomTextField(
                controller: _netIncomeController,
                label: 'Résultat Net (DH)',
                keyboardType: TextInputType.number,
                validator: _validateNumber,
              ),
              const SizedBox(height: 12),
              CustomTextField(
                controller: _interestExpenseController,
                label: 'Charges Financières (DH)',
                keyboardType: TextInputType.number,
                validator: _validateNumber,
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  Widget _buildResultsTab() {
    if (_result == null) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.analytics_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'Saisissez les données et calculez\npour voir les résultats',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }
    
    return SingleChildScrollView(
      padding: context.responsivePadding(
        mobile: const EdgeInsets.all(12),
        tablet: const EdgeInsets.all(16),
        desktop: const EdgeInsets.all(20),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildRatioSection('Ratios de Liquidité', _result!.liquidityRatios),
          SizedBox(height: ResponsiveBreakpointsExtension(context).responsiveSpacing(mobile: 12, tablet: 16, desktop: 20)),
          _buildRatioSection('Ratios de Rentabilité', _result!.profitabilityRatios),
          SizedBox(height: ResponsiveBreakpointsExtension(context).responsiveSpacing(mobile: 12, tablet: 16, desktop: 20)),
          _buildRatioSection('Ratios d\'Activité', _result!.activityRatios),
          SizedBox(height: ResponsiveBreakpointsExtension(context).responsiveSpacing(mobile: 12, tablet: 16, desktop: 20)),
          _buildRatioSection('Ratios d\'Endettement', _result!.leverageRatios),
          SizedBox(height: ResponsiveBreakpointsExtension(context).responsiveSpacing(mobile: 12, tablet: 16, desktop: 20)),
          _buildAnalysisSection(),
          SizedBox(height: ResponsiveBreakpointsExtension(context).responsiveSpacing(mobile: 12, tablet: 16, desktop: 20)),
          _buildRecommendationsSection(),
        ],
      ),
    );
  }
  
  Widget _buildSectionCard(String title, List<Widget> children) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: DesignTokens.colorPrimary,
              ),
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }
  
  Widget _buildRatioSection(String title, dynamic ratios) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: DesignTokens.colorPrimary,
              ),
            ),
            const SizedBox(height: 12),
            _buildRatioItems(ratios),
          ],
        ),
      ),
    );
  }
  
  Widget _buildRatioItems(dynamic ratios) {
    if (ratios is LiquidityRatios) {
      return Column(
        children: [
          _buildRatioItem('Ratio de Liquidité Générale', ratios.currentRatio, ''),
          _buildRatioItem('Ratio de Liquidité Réduite', ratios.quickRatio, ''),
          _buildRatioItem('Ratio de Liquidité Immédiate', ratios.cashRatio, ''),
          _buildRatioItem('Fonds de Roulement', ratios.workingCapital, 'DH'),
        ],
      );
    } else if (ratios is ProfitabilityRatios) {
      return Column(
        children: [
          _buildRatioItem('Rentabilité des Actifs (ROA)', ratios.returnOnAssets * 100, '%'),
          _buildRatioItem('Rentabilité des Capitaux Propres (ROE)', ratios.returnOnEquity * 100, '%'),
          _buildRatioItem('Marge Nette', ratios.netProfitMargin * 100, '%'),
          _buildRatioItem('Marge Brute', ratios.grossProfitMargin * 100, '%'),
          _buildRatioItem('Marge d\'Exploitation', ratios.operatingMargin * 100, '%'),
        ],
      );
    } else if (ratios is ActivityRatios) {
      return Column(
        children: [
          _buildRatioItem('Rotation des Stocks', ratios.inventoryTurnover, 'fois/an'),
          _buildRatioItem('Rotation des Créances', ratios.receivablesTurnover, 'fois/an'),
          _buildRatioItem('Rotation des Actifs', ratios.assetTurnover, 'fois/an'),
          _buildRatioItem('Durée de Stockage', ratios.inventoryDays, 'jours'),
          _buildRatioItem('Durée de Recouvrement', ratios.receivablesDays, 'jours'),
        ],
      );
    } else if (ratios is LeverageRatios) {
      return Column(
        children: [
          _buildRatioItem('Ratio d\'Endettement', ratios.debtToEquity, ''),
          _buildRatioItem('Ratio de Dettes', ratios.debtRatio * 100, '%'),
          _buildRatioItem('Couverture des Intérêts', ratios.interestCoverage, 'fois'),
          _buildRatioItem('Ratio de Capitaux Propres', ratios.equityRatio * 100, '%'),
        ],
      );
    }
    return const SizedBox.shrink();
  }
  
  Widget _buildRatioItem(String label, double value, String unit) {
    Color valueColor = DesignTokens.colorOnSurface;
    
    // Color coding based on ratio type and value
    if (label.contains('Liquidité') && value < 1.0) {
      valueColor = Colors.red;
    } else if (label.contains('Rentabilité') && value < 5.0) {
      valueColor = Colors.orange;
    } else if (value > 0) {
      valueColor = DesignTokens.colorPrimary;
    }
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
          Text(
            '${value.toStringAsFixed(2)} $unit',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: valueColor,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildAnalysisSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Analyse Globale',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: DesignTokens.colorPrimary,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              _result!.overallAnalysis,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildRecommendationsSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recommandations',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: DesignTokens.colorPrimary,
              ),
            ),
            const SizedBox(height: 12),
            ..._result!.recommendations.map(
              (recommendation) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Icon(
                      Icons.lightbulb_outline,
                      size: 16,
                      color: Colors.orange,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        recommendation,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildCalculateButton() {
    final isMobile = context.isMobile;

    if (isMobile) {
      return FloatingActionButton.extended(
        onPressed: _isCalculating ? null : _calculateRatios,
        backgroundColor: DesignTokens.colorPrimary,
        icon: _isCalculating
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Icon(Icons.calculate),
        label: Text(_isCalculating ? 'Calcul...' : 'Calculer'),
      );
    } else {
      return FloatingActionButton.extended(
        onPressed: _isCalculating ? null : _calculateRatios,
        backgroundColor: DesignTokens.colorPrimary,
        icon: _isCalculating
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Icon(Icons.calculate),
        label: Text(_isCalculating ? 'Calculer les ratios' : 'Calculer les ratios'),
      );
    }
  }
  
  String? _validateNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Ce champ est requis';
    }
    if (double.tryParse(value) == null) {
      return 'Veuillez saisir un nombre valide';
    }
    return null;
  }
  
  void _calculateRatios() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    
    setState(() {
      _isCalculating = true;
    });
    
    try {
      final input = FinancialRatiosInput(
        currentAssets: double.parse(_currentAssetsController.text),
        currentLiabilities: double.parse(_currentLiabilitiesController.text),
        totalAssets: double.parse(_totalAssetsController.text),
        totalEquity: double.parse(_totalEquityController.text),
        inventory: double.parse(_inventoryController.text),
        cash: double.parse(_cashController.text),
        accountsReceivable: double.parse(_accountsReceivableController.text),
        totalDebt: double.parse(_totalDebtController.text),
        longTermDebt: double.parse(_longTermDebtController.text),
        revenue: double.parse(_revenueController.text),
        netIncome: double.parse(_netIncomeController.text),
        costOfGoodsSold: double.parse(_costOfGoodsSoldController.text),
        operatingIncome: double.parse(_operatingIncomeController.text),
        interestExpense: double.parse(_interestExpenseController.text),
        grossProfit: double.parse(_grossProfitController.text),
      );
      
      final service = FinancialRatiosService();
      final result = service.calculateRatios(input);
      
      setState(() {
        _result = result;
        _isCalculating = false;
      });
      
      // Switch to results tab
      _tabController.animateTo(2);
      
    } catch (e) {
      setState(() {
        _isCalculating = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du calcul: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
